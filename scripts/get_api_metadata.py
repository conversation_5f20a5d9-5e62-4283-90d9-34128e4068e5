#!/usr/bin/env python3
"""
Helper script to fetch valid entity types, categories, and tags from the AI Navigator API.
This helps us create valid test payloads for live integration tests.
"""

import os
import asyncio
import aiohttp
import json
from dotenv import load_dotenv

load_dotenv()

async def fetch_api_metadata():
    """
    Fetch metadata from the AI Navigator API to understand valid values.
    """
    api_url = os.getenv("AI_NAV_API_URL", "https://ai-nav.onrender.com")
    auth_token = os.getenv("AI_NAV_AUTH_TOKEN")
    
    if not auth_token:
        print("❌ Error: AI_NAV_AUTH_TOKEN not found in .env file")
        return None
    
    print(f"🔍 Fetching API metadata from: {api_url}")
    
    headers = {
        "Authorization": f"Bearer {auth_token}",
        "Content-Type": "application/json",
        "Accept": "application/json"
    }
    
    # Endpoints to try for getting metadata
    metadata_endpoints = [
        "/entity-types",
        "/api/entity-types", 
        "/categories",
        "/api/categories",
        "/tags",
        "/api/tags",
        "/metadata",
        "/api/metadata"
    ]
    
    metadata = {}
    
    async with aiohttp.ClientSession() as session:
        for endpoint in metadata_endpoints:
            try:
                url = f"{api_url.rstrip('/')}{endpoint}"
                print(f"\n🔍 Trying endpoint: {url}")
                
                async with session.get(url, headers=headers, timeout=aiohttp.ClientTimeout(total=30)) as response:
                    print(f"   Response status: {response.status}")
                    
                    if response.status == 200:
                        try:
                            data = await response.json()
                            print(f"   ✅ Success! Got {len(data) if isinstance(data, list) else 'data'} items")
                            
                            # Store the data with a clean endpoint name
                            endpoint_name = endpoint.replace('/api/', '').replace('/', '')
                            metadata[endpoint_name] = data
                            
                            # Show a preview of the data
                            if isinstance(data, list) and len(data) > 0:
                                first_item = data[0]
                                if isinstance(first_item, dict):
                                    print(f"   Sample item keys: {list(first_item.keys())}")
                                    if 'id' in first_item:
                                        print(f"   Sample ID: {first_item['id']}")
                                    if 'name' in first_item:
                                        print(f"   Sample name: {first_item['name']}")
                            
                        except json.JSONDecodeError as e:
                            print(f"   ⚠️  JSON decode error: {e}")
                            response_text = await response.text()
                            print(f"   Response preview: {response_text[:200]}...")
                    
                    elif response.status == 404:
                        print(f"   ⚠️  Endpoint not found")
                    
                    elif response.status == 401:
                        print(f"   ❌ Authentication failed")
                        break
                    
                    else:
                        response_text = await response.text()
                        print(f"   ❌ Error {response.status}: {response_text[:200]}...")
                        
            except aiohttp.ClientError as e:
                print(f"   ❌ Connection error: {e}")
                continue
            except Exception as e:
                print(f"   ❌ Unexpected error: {e}")
                continue
    
    return metadata

async def save_metadata(metadata):
    """
    Save metadata to a machine-readable JSON file for pipeline use.
    """
    print(f"\n💾 Saving metadata to arep/api/metadata.json...")

    # Create the metadata structure for pipeline use
    pipeline_metadata = {
        "entity_types": {},
        "categories": {},
        "tags": {},
        "last_updated": None
    }

    # Process entity types
    entity_types = metadata.get('entity-types', [])
    if entity_types and isinstance(entity_types, list):
        for et in entity_types:
            if isinstance(et, dict) and 'name' in et and 'id' in et:
                pipeline_metadata["entity_types"][et['name']] = et['id']
        print(f"   ✅ Processed {len(pipeline_metadata['entity_types'])} entity types")

    # Process categories
    categories = metadata.get('categories', [])
    if categories and isinstance(categories, list):
        for cat in categories:
            if isinstance(cat, dict) and 'name' in cat and 'id' in cat:
                pipeline_metadata["categories"][cat['name']] = cat['id']
        print(f"   ✅ Processed {len(pipeline_metadata['categories'])} categories")

    # Process tags
    tags = metadata.get('tags', [])
    if tags and isinstance(tags, list):
        for tag in tags:
            if isinstance(tag, dict) and 'name' in tag and 'id' in tag:
                pipeline_metadata["tags"][tag['name']] = tag['id']
        print(f"   ✅ Processed {len(pipeline_metadata['tags'])} tags")

    # Add timestamp
    from datetime import datetime
    pipeline_metadata["last_updated"] = datetime.utcnow().isoformat()

    # Ensure the directory exists
    import os
    os.makedirs("arep/api", exist_ok=True)

    # Save to file
    try:
        with open("arep/api/metadata.json", "w") as f:
            json.dump(pipeline_metadata, f, indent=2)
        print(f"   ✅ Successfully saved metadata to arep/api/metadata.json")
        return True
    except Exception as e:
        print(f"   ❌ Failed to save metadata: {e}")
        return False


async def create_valid_test_payload(metadata):
    """
    Create a valid test payload using the fetched metadata.
    """
    print(f"\n📋 Creating valid test payload...")

    # Find a valid entity type (preferably "Tool")
    entity_type_id = None
    entity_types = metadata.get('entity-types', [])

    if entity_types:
        # Look for a "Tool" entity type first
        for et in entity_types:
            if isinstance(et, dict) and et.get('name', '').lower() in ['tool', 'tools']:
                entity_type_id = et.get('id')
                print(f"   ✅ Found Tool entity type: {entity_type_id}")
                break

        # If no "Tool" found, use the first available
        if not entity_type_id and entity_types:
            entity_type_id = entity_types[0].get('id')
            entity_type_name = entity_types[0].get('name', 'Unknown')
            print(f"   ✅ Using first available entity type: {entity_type_name} ({entity_type_id})")

    # Find valid categories
    category_ids = []
    categories = metadata.get('categories', [])
    if categories and isinstance(categories, list):
        # Take the first category
        first_category = categories[0]
        if isinstance(first_category, dict) and 'id' in first_category:
            category_ids = [first_category['id']]
            category_name = first_category.get('name', 'Unknown')
            print(f"   ✅ Using category: {category_name} ({first_category['id']})")

    # Find valid tags
    tag_ids = []
    tags = metadata.get('tags', [])
    if tags and isinstance(tags, list):
        # Take the first tag
        first_tag = tags[0]
        if isinstance(first_tag, dict) and 'id' in first_tag:
            tag_ids = [first_tag['id']]
            tag_name = first_tag.get('name', 'Unknown')
            print(f"   ✅ Using tag: {tag_name} ({first_tag['id']})")

    # Create the payload
    payload = {
        "name": "[TEST] Live Integration Tool",
        "website_url": "https://example-test.com/live-integration",
        "short_description": "A temporary tool for live integration testing.",
        "logo_url": "https://www.google.com/s2/favicons?domain=example.com&sz=128",
        "status": "PENDING"
    }

    # Add required fields if we found them
    if entity_type_id:
        payload["entity_type_id"] = entity_type_id

    if category_ids:
        payload["category_ids"] = category_ids

    if tag_ids:
        payload["tag_ids"] = tag_ids

    # Add minimal tool details to satisfy validation
    payload["tool_details"] = {
        "key_features": ["Integration testing", "Temporary entity", "Cleanup test"],
        "has_free_tier": True,
        "use_cases": ["Testing"],
        "integrations": ["API"],
        "programming_languages": ["Python"],
        "frameworks": ["FastAPI"],
        "libraries": ["aiohttp"],
        "target_audience": ["Developers"],
        "deployment_options": ["Cloud"],
        "supported_os": ["Linux"],
        "support_channels": ["Email"]
    }

    return payload

async def main():
    """
    Main function to fetch metadata and create a valid test payload.
    """
    print("🚀 AI Navigator API Metadata Fetcher")
    print("=" * 50)

    # Fetch metadata
    metadata = await fetch_api_metadata()

    if not metadata:
        print(f"\n❌ Could not fetch any metadata from the API")
        return

    print(f"\n📊 Metadata Summary:")
    print("=" * 30)
    for key, value in metadata.items():
        if isinstance(value, list):
            print(f"   {key}: {len(value)} items")
        else:
            print(f"   {key}: {type(value).__name__}")

    # Save metadata for pipeline use
    metadata_saved = await save_metadata(metadata)
    if not metadata_saved:
        print(f"\n⚠️  Warning: Could not save metadata for pipeline use")

    # Create a valid test payload
    payload = await create_valid_test_payload(metadata)

    print(f"\n📋 Generated Test Payload:")
    print("=" * 30)
    print(json.dumps(payload, indent=2))

    # Save to file for easy use
    output_file = "test_payload.json"
    with open(output_file, 'w') as f:
        json.dump(payload, f, indent=2)

    print(f"\n✅ Saved test payload to: {output_file}")
    print(f"   You can use this payload to update the live integration test")

    if metadata_saved:
        print(f"\n🎯 Pipeline metadata ready!")
        print(f"   The pipeline can now use real UUIDs from arep/api/metadata.json")

if __name__ == "__main__":
    asyncio.run(main())
