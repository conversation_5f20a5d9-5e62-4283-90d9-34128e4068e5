"""
Tool-specific enhancer for AI Resource Enhancement Pipeline.
Transforms research data into tool-specific API format.
"""

import re
from typing import Any, Dict, List, Optional

from arep.api.models import Resource, ToolDetails
from arep.enhancement.base import BaseEnhancer, EnhancementError
from arep.models import ClassifiedEntity, ResearchData
from arep.utils.logger import get_logger

logger = get_logger(__name__)


class ToolEnhancer(BaseEnhancer):
    """
    Enhancer specifically for AI tools and software.

    Transforms research data into ToolDetails format with:
    - Key features extraction
    - API availability detection
    - Free tier analysis
    - Use case identification
    - Integration capabilities
    """

    def __init__(self):
        super().__init__("tool")

        # Load metadata for real UUIDs
        self.metadata = self._load_metadata()
        self.category_map = self.metadata.get("categories", {}) if self.metadata else {}
        self.tag_map = self.metadata.get("tags", {}) if self.metadata else {}

    def _load_metadata(self):
        """Load metadata from the JSON file."""
        import json
        import os

        metadata_path = "arep/api/metadata.json"
        if os.path.exists(metadata_path):
            try:
                with open(metadata_path, "r") as f:
                    metadata = json.load(f)
                logger.info(f"Loaded metadata with {len(metadata.get('categories', {}))} categories and {len(metadata.get('tags', {}))} tags")
                return metadata
            except Exception as e:
                logger.warning(f"Failed to load metadata from {metadata_path}: {e}")
        else:
            logger.warning(f"Metadata file not found at {metadata_path}. Run scripts/get_api_metadata.py first.")

        return None

    async def enhance(
        self, entity: ClassifiedEntity, research_data: ResearchData
    ) -> Resource:
        """
        Transform classified entity and research data into tool-specific Resource.

        Args:
            entity: Classified entity with basic information
            research_data: Research data gathered about the entity

        Returns:
            Resource object with ToolDetails ready for API submission
        """
        self.logger.info(f"Enhancing tool entity: {entity.name}")

        try:
            # Extract base fields common to all entities
            base_fields = self._extract_base_fields(entity, research_data)

            # Handle required arrays with defaults
            base_fields = self._ensure_required_arrays(base_fields, research_data)

            # Create tool-specific details
            tool_details = await self._create_tool_details(entity, research_data)

            # Validate required fields
            required_fields = ["name", "website_url", "entity_type_id"]
            if not self._validate_required_fields(base_fields, required_fields):
                raise EnhancementError(
                    f"Missing required fields for tool: {entity.name}",
                    entity_name=entity.name,
                    entity_type="tool",
                )

            # Create Resource object
            resource = Resource(**base_fields, tool_details=tool_details)

            self.logger.info(f"Successfully enhanced tool: {entity.name}")
            return resource

        except Exception as e:
            self.logger.error(f"Failed to enhance tool {entity.name}: {e}")
            raise EnhancementError(
                f"Tool enhancement failed: {e}",
                entity_name=entity.name,
                entity_type="tool",
            )

    async def _create_tool_details(
        self, entity: ClassifiedEntity, research_data: ResearchData
    ) -> ToolDetails:
        """
        Create ToolDetails object from research data.

        Args:
            entity: Classified entity
            research_data: Research data

        Returns:
            ToolDetails object
        """
        # Extract key features
        key_features = self._extract_key_features(research_data)

        # Detect API availability
        has_api = self._detect_api_availability(research_data)

        # Determine free tier availability
        has_free_tier = self._detect_free_tier(research_data)

        # Extract use cases
        use_cases = self._extract_use_cases(research_data)

        # Detect integration capabilities
        integrations = self._detect_integrations(research_data)

        # Extract supported platforms
        supported_platforms = self._extract_platforms(research_data)

        # Determine deployment options
        deployment_options = self._extract_deployment_options(research_data)

        # Extract additional required arrays with defaults
        programming_languages = self._extract_programming_languages(research_data)
        frameworks = self._extract_frameworks(research_data)
        libraries = self._extract_libraries(research_data)
        target_audience = self._extract_target_audience(research_data)
        supported_os = self._extract_supported_os(research_data)
        support_channels = self._extract_support_channels(research_data)

        tool_details = ToolDetails(
            key_features=key_features,
            has_api=has_api,
            has_free_tier=has_free_tier,
            use_cases=use_cases,
            integrations=integrations,  # Note: using 'integrations' not 'integrations_available'
            platforms=supported_platforms,  # Note: using 'platforms' not 'supported_platforms'
            deployment_options=deployment_options,
            programming_languages=programming_languages,
            frameworks=frameworks,
            libraries=libraries,
            target_audience=target_audience,
            supported_os=supported_os,
            support_channels=support_channels,
        )

        return tool_details

    def _ensure_required_arrays(self, base_fields: Dict[str, Any], research_data: ResearchData) -> Dict[str, Any]:
        """
        Ensure required arrays are populated with real UUIDs or defaults.

        Args:
            base_fields: Base fields extracted from entity and research data
            research_data: Research data

        Returns:
            Updated base fields with required arrays
        """
        # Handle category_ids
        category_ids = []
        if "category_names" in base_fields and base_fields["category_names"]:
            # Convert category names to UUIDs
            for category_name in base_fields["category_names"]:
                category_uuid = self._get_category_uuid(category_name)
                if category_uuid:
                    category_ids.append(category_uuid)

        # If no categories found, use default
        if not category_ids and self.category_map:
            # Try to find a good default category for tools
            default_categories = ["AI Tools", "Tools", "Software", "Technology", "General"]
            for default_cat in default_categories:
                default_uuid = self._get_category_uuid(default_cat)
                if default_uuid:
                    category_ids = [default_uuid]
                    logger.info(f"Using default category: {default_cat}")
                    break

            # If still no category, use the first available
            if not category_ids:
                first_category = list(self.category_map.values())[0] if self.category_map else None
                if first_category:
                    category_ids = [first_category]
                    logger.info(f"Using first available category")

        # Handle tag_ids
        tag_ids = []
        if "tag_names" in base_fields and base_fields["tag_names"]:
            # Convert tag names to UUIDs
            for tag_name in base_fields["tag_names"]:
                tag_uuid = self._get_tag_uuid(tag_name)
                if tag_uuid:
                    tag_ids.append(tag_uuid)

        # If no tags found, use default
        if not tag_ids and self.tag_map:
            # Try to find a good default tag for tools
            default_tags = ["AI", "Tool", "Software", "Technology", "General"]
            for default_tag in default_tags:
                default_uuid = self._get_tag_uuid(default_tag)
                if default_uuid:
                    tag_ids = [default_uuid]
                    logger.info(f"Using default tag: {default_tag}")
                    break

            # If still no tag, use the first available
            if not tag_ids:
                first_tag = list(self.tag_map.values())[0] if self.tag_map else None
                if first_tag:
                    tag_ids = [first_tag]
                    logger.info(f"Using first available tag")

        # Update base_fields with UUIDs
        base_fields["category_ids"] = category_ids
        base_fields["tag_ids"] = tag_ids

        # Remove the name-based fields as they're not needed for the API
        base_fields.pop("category_names", None)
        base_fields.pop("tag_names", None)

        return base_fields

    def _get_category_uuid(self, category_name: str) -> Optional[str]:
        """Get UUID for a category name."""
        if not self.category_map:
            return None

        # Try exact match first
        if category_name in self.category_map:
            return self.category_map[category_name]

        # Try case-insensitive match
        for name, uuid in self.category_map.items():
            if name.lower() == category_name.lower():
                return uuid

        # Try partial match
        category_lower = category_name.lower()
        for name, uuid in self.category_map.items():
            name_lower = name.lower()
            if category_lower in name_lower or name_lower in category_lower:
                return uuid

        return None

    def _get_tag_uuid(self, tag_name: str) -> Optional[str]:
        """Get UUID for a tag name."""
        if not self.tag_map:
            return None

        # Try exact match first
        if tag_name in self.tag_map:
            return self.tag_map[tag_name]

        # Try case-insensitive match
        for name, uuid in self.tag_map.items():
            if name.lower() == tag_name.lower():
                return uuid

        # Try partial match
        tag_lower = tag_name.lower()
        for name, uuid in self.tag_map.items():
            name_lower = name.lower()
            if tag_lower in name_lower or name_lower in tag_lower:
                return uuid

        return None

    def _extract_key_features(self, research_data: ResearchData) -> List[str]:
        """Extract key features from research data."""
        features = self._extract_features_list(research_data, max_features=8)

        # If no features found, try to extract from description
        if not features and research_data.description:
            features = self._extract_features_from_text(research_data.description)

        # Ensure we have at least some features
        if not features:
            features = ["AI-powered capabilities", "User-friendly interface"]

        return features

    def _extract_features_from_text(self, text: str) -> List[str]:
        """Extract features from descriptive text using patterns."""
        features = []

        # Common feature patterns
        feature_patterns = [
            r"(?:features?|capabilities?|offers?|provides?|includes?)[:\s]+([^.!?]+)",
            r"(?:can|able to|allows?)[:\s]+([^.!?]+)",
            r"(?:supports?|enables?)[:\s]+([^.!?]+)",
        ]

        for pattern in feature_patterns:
            matches = re.findall(pattern, text, re.IGNORECASE)
            for match in matches:
                # Clean and split the match
                clean_features = [f.strip() for f in match.split(",") if f.strip()]
                features.extend(clean_features[:3])  # Limit per pattern

        return features[:5]  # Return top 5 features

    def _detect_api_availability(self, research_data: ResearchData) -> bool:
        """Detect if the tool has an API."""
        # Check in features
        if research_data.features:
            for feature in research_data.features:
                if any(
                    term in feature.lower()
                    for term in ["api", "rest", "graphql", "webhook"]
                ):
                    return True

        # Check in description
        if research_data.description:
            api_terms = [
                "api",
                "rest api",
                "graphql",
                "webhook",
                "integration",
                "developer",
            ]
            if any(term in research_data.description.lower() for term in api_terms):
                return True

        # Check in technical details
        if research_data.technical_details:
            tech_text = str(research_data.technical_details).lower()
            if any(term in tech_text for term in ["api", "rest", "graphql"]):
                return True

        return False

    def _detect_free_tier(self, research_data: ResearchData) -> bool:
        """Detect if the tool has a free tier."""
        pricing_tier = self._determine_pricing_tier(research_data)
        return pricing_tier in ["free", "freemium"]

    def _extract_use_cases(self, research_data: ResearchData) -> List[str]:
        """Extract use cases from research data."""
        use_cases = []

        # Common AI tool use cases
        common_use_cases = [
            "content creation",
            "data analysis",
            "automation",
            "machine learning",
            "natural language processing",
            "image processing",
            "chatbots",
            "recommendation systems",
            "predictive analytics",
            "workflow optimization",
        ]

        # Check description for use cases
        if research_data.description:
            desc_lower = research_data.description.lower()
            for use_case in common_use_cases:
                if use_case in desc_lower:
                    use_cases.append(use_case.title())

        # If no specific use cases found, infer from entity type and features
        if not use_cases:
            use_cases = ["AI automation", "Business intelligence"]

        return use_cases[:5]  # Limit to 5 use cases

    def _detect_integrations(self, research_data: ResearchData) -> List[str]:
        """Detect available integrations."""
        integrations = []

        # Common integration platforms
        integration_platforms = [
            "slack",
            "discord",
            "teams",
            "zapier",
            "salesforce",
            "hubspot",
            "google",
            "microsoft",
            "aws",
            "azure",
            "github",
            "jira",
        ]

        # Check description and features for integrations
        search_text = ""
        if research_data.description:
            search_text += research_data.description.lower()
        if research_data.features:
            search_text += " ".join(research_data.features).lower()

        for platform in integration_platforms:
            if platform in search_text:
                integrations.append(platform.title())

        # Ensure at least one integration (required by API)
        if not integrations:
            integrations = ["API"]

        return integrations[:8]  # Limit to 8 integrations

    def _extract_platforms(self, research_data: ResearchData) -> List[str]:
        """Extract supported platforms."""
        platforms = []

        # Common platforms
        platform_keywords = {
            "web": ["web", "browser", "online"],
            "mobile": ["mobile", "ios", "android", "app"],
            "desktop": ["desktop", "windows", "mac", "linux"],
            "cloud": ["cloud", "saas", "hosted"],
        }

        search_text = ""
        if research_data.description:
            search_text += research_data.description.lower()
        if research_data.technical_details:
            search_text += str(research_data.technical_details).lower()

        for platform, keywords in platform_keywords.items():
            if any(keyword in search_text for keyword in keywords):
                platforms.append(platform.title())

        # Default to web if no platforms detected
        if not platforms:
            platforms = ["Web"]

        return platforms

    def _extract_deployment_options(self, research_data: ResearchData) -> List[str]:
        """Extract deployment options."""
        deployment_options = []

        # Common deployment patterns
        deployment_keywords = {
            "cloud": ["cloud", "saas", "hosted", "online"],
            "on-premise": ["on-premise", "self-hosted", "private"],
            "hybrid": ["hybrid", "multi-cloud"],
            "api": ["api", "service", "endpoint"],
        }

        search_text = ""
        if research_data.description:
            search_text += research_data.description.lower()
        if research_data.technical_details:
            search_text += str(research_data.technical_details).lower()

        for deployment, keywords in deployment_keywords.items():
            if any(keyword in search_text for keyword in keywords):
                deployment_options.append(deployment.replace("-", " ").title())

        # Default deployment option
        if not deployment_options:
            deployment_options = ["Cloud"]

        return deployment_options

    def _extract_programming_languages(self, research_data: ResearchData) -> List[str]:
        """Extract programming languages from research data."""
        languages = []

        # Common programming languages for AI tools
        language_keywords = [
            "python", "javascript", "typescript", "java", "c++", "c#", "go",
            "rust", "swift", "kotlin", "php", "ruby", "scala", "r", "matlab"
        ]

        search_text = ""
        if research_data.description:
            search_text += research_data.description.lower()
        if research_data.technical_details:
            search_text += str(research_data.technical_details).lower()

        for lang in language_keywords:
            if lang in search_text:
                languages.append(lang.title())

        # Default if none found
        if not languages:
            languages = ["Python"]

        return languages[:5]  # Limit to 5 languages

    def _extract_frameworks(self, research_data: ResearchData) -> List[str]:
        """Extract frameworks from research data."""
        frameworks = []

        # Common frameworks for AI tools
        framework_keywords = [
            "tensorflow", "pytorch", "keras", "scikit-learn", "fastapi", "flask",
            "django", "react", "vue", "angular", "express", "spring", "laravel"
        ]

        search_text = ""
        if research_data.description:
            search_text += research_data.description.lower()
        if research_data.technical_details:
            search_text += str(research_data.technical_details).lower()

        for framework in framework_keywords:
            if framework in search_text:
                frameworks.append(framework.title())

        # Default if none found
        if not frameworks:
            frameworks = ["FastAPI"]

        return frameworks[:5]  # Limit to 5 frameworks

    def _extract_libraries(self, research_data: ResearchData) -> List[str]:
        """Extract libraries from research data."""
        libraries = []

        # Common libraries for AI tools
        library_keywords = [
            "numpy", "pandas", "requests", "aiohttp", "sqlalchemy", "pydantic",
            "opencv", "pillow", "matplotlib", "seaborn", "plotly", "streamlit"
        ]

        search_text = ""
        if research_data.description:
            search_text += research_data.description.lower()
        if research_data.technical_details:
            search_text += str(research_data.technical_details).lower()

        for library in library_keywords:
            if library in search_text:
                libraries.append(library.title())

        # Default if none found
        if not libraries:
            libraries = ["aiohttp"]

        return libraries[:5]  # Limit to 5 libraries

    def _extract_target_audience(self, research_data: ResearchData) -> List[str]:
        """Extract target audience from research data."""
        audiences = []

        # Common target audiences for AI tools
        audience_keywords = {
            "Developers": ["developer", "programmer", "engineer", "coder"],
            "Data Scientists": ["data scientist", "analyst", "researcher"],
            "Business Users": ["business", "manager", "executive", "entrepreneur"],
            "Students": ["student", "learner", "education"],
            "Researchers": ["researcher", "academic", "scientist"],
            "Designers": ["designer", "creative", "artist"],
            "Marketers": ["marketer", "marketing", "sales"]
        }

        search_text = ""
        if research_data.description:
            search_text += research_data.description.lower()

        for audience, keywords in audience_keywords.items():
            if any(keyword in search_text for keyword in keywords):
                audiences.append(audience)

        # Default if none found
        if not audiences:
            audiences = ["Developers"]

        return audiences[:3]  # Limit to 3 audiences

    def _extract_supported_os(self, research_data: ResearchData) -> List[str]:
        """Extract supported operating systems from research data."""
        os_list = []

        # Common OS keywords
        os_keywords = {
            "Windows": ["windows", "win"],
            "macOS": ["mac", "macos", "osx"],
            "Linux": ["linux", "ubuntu", "debian", "centos"],
            "iOS": ["ios", "iphone", "ipad"],
            "Android": ["android"]
        }

        search_text = ""
        if research_data.description:
            search_text += research_data.description.lower()
        if research_data.technical_details:
            search_text += str(research_data.technical_details).lower()

        for os_name, keywords in os_keywords.items():
            if any(keyword in search_text for keyword in keywords):
                os_list.append(os_name)

        # Default if none found
        if not os_list:
            os_list = ["Linux"]

        return os_list

    def _extract_support_channels(self, research_data: ResearchData) -> List[str]:
        """Extract support channels from research data."""
        channels = []

        # Common support channels
        channel_keywords = {
            "Email": ["email", "support@", "contact@"],
            "Live Chat": ["chat", "live chat", "support chat"],
            "Documentation": ["docs", "documentation", "guide"],
            "Community Forum": ["forum", "community", "discussion"],
            "GitHub Issues": ["github", "issues", "bug report"],
            "Discord": ["discord"],
            "Slack": ["slack"]
        }

        search_text = ""
        if research_data.description:
            search_text += research_data.description.lower()
        if research_data.contact_info:
            search_text += research_data.contact_info.lower()

        for channel, keywords in channel_keywords.items():
            if any(keyword in search_text for keyword in keywords):
                channels.append(channel)

        # Default if none found
        if not channels:
            channels = ["Email"]

        return channels[:4]  # Limit to 4 channels
